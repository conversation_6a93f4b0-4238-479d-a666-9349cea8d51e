-- 创建商品服务数据库
CREATE DATABASE IF NOT EXISTS pay_mall_product CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE pay_mall_product;

-- 创建分类表
CREATE TABLE IF NOT EXISTS categories (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id VARCHAR(36),
    level INT NOT NULL DEFAULT 1,
    path VARCHAR(500),
    icon_url VARCHAR(500),
    sort_order INT NOT NULL DEFAULT 0,
    status ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建商品表
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    image_url VARCHAR(500),
    image_urls JSON,
    category_id VARCHAR(36) NOT NULL,
    brand VARCHAR(100),
    sku VARCHAR(100) UNIQUE,
    weight DECIMAL(8,3),
    dimensions JSON,
    tags JSON,
    attributes JSON,
    status ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED', 'DELETED') NOT NULL DEFAULT 'DRAFT',
    sort_order INT NOT NULL DEFAULT 0,
    view_count INT NOT NULL DEFAULT 0,
    sale_count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_id (category_id),
    INDEX idx_brand (brand),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_price (price),
    INDEX idx_sort_order (sort_order),
    INDEX idx_view_count (view_count),
    INDEX idx_sale_count (sale_count),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at),
    
    -- 全文搜索索引
    FULLTEXT idx_search (name, description),
    
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建商品变体表
CREATE TABLE IF NOT EXISTS product_variants (
    id VARCHAR(36) PRIMARY KEY,
    product_id VARCHAR(36) NOT NULL,
    sku VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    attributes JSON NOT NULL,
    image_url VARCHAR(500),
    status ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED', 'DELETED') NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_product_id (product_id),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_price (price),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例分类数据
INSERT INTO categories (id, name, description, parent_id, level, path, sort_order, status) VALUES
('cat-1', '电子产品', '各类电子产品', NULL, 1, '/电子产品', 1, 'ACTIVE'),
('cat-2', '服装鞋帽', '服装、鞋子、帽子等', NULL, 1, '/服装鞋帽', 2, 'ACTIVE'),
('cat-3', '家居用品', '家庭日用品', NULL, 1, '/家居用品', 3, 'ACTIVE'),
('cat-4', '手机数码', '手机及数码产品', 'cat-1', 2, '/电子产品/手机数码', 1, 'ACTIVE'),
('cat-5', '电脑办公', '电脑及办公用品', 'cat-1', 2, '/电子产品/电脑办公', 2, 'ACTIVE'),
('cat-6', '男装', '男士服装', 'cat-2', 2, '/服装鞋帽/男装', 1, 'ACTIVE'),
('cat-7', '女装', '女士服装', 'cat-2', 2, '/服装鞋帽/女装', 2, 'ACTIVE'),
('cat-8', '智能手机', '各品牌智能手机', 'cat-4', 3, '/电子产品/手机数码/智能手机', 1, 'ACTIVE'),
('cat-9', '笔记本电脑', '各品牌笔记本电脑', 'cat-5', 3, '/电子产品/电脑办公/笔记本电脑', 1, 'ACTIVE');

-- 插入示例商品数据
INSERT INTO products (id, name, description, price, original_price, image_url, category_id, brand, sku, status, sort_order) VALUES
('prod-1', 'iPhone 15 Pro', '苹果最新旗舰手机，搭载A17 Pro芯片', 7999.00, 8999.00, 'https://example.com/iphone15pro.jpg', 'cat-8', 'Apple', 'IPHONE15PRO-128GB', 'ACTIVE', 1),
('prod-2', 'MacBook Pro 14英寸', '苹果专业级笔记本电脑，M3芯片', 14999.00, 15999.00, 'https://example.com/macbookpro14.jpg', 'cat-9', 'Apple', 'MBP14-M3-512GB', 'ACTIVE', 1),
('prod-3', '小米14', '小米旗舰手机，骁龙8 Gen3处理器', 3999.00, 4299.00, 'https://example.com/mi14.jpg', 'cat-8', '小米', 'MI14-256GB', 'ACTIVE', 2),
('prod-4', 'ThinkPad X1 Carbon', '联想商务笔记本，轻薄便携', 12999.00, NULL, 'https://example.com/thinkpadx1.jpg', 'cat-9', 'Lenovo', 'X1C-I7-16GB', 'ACTIVE', 2);

-- 插入示例商品变体数据
INSERT INTO product_variants (id, product_id, sku, name, price, attributes, status) VALUES
('var-1', 'prod-1', 'IPHONE15PRO-128GB-BLACK', 'iPhone 15 Pro 128GB 深空黑色', 7999.00, '{"color": "深空黑色", "storage": "128GB"}', 'ACTIVE'),
('var-2', 'prod-1', 'IPHONE15PRO-256GB-BLACK', 'iPhone 15 Pro 256GB 深空黑色', 8999.00, '{"color": "深空黑色", "storage": "256GB"}', 'ACTIVE'),
('var-3', 'prod-1', 'IPHONE15PRO-128GB-WHITE', 'iPhone 15 Pro 128GB 白色钛金属', 7999.00, '{"color": "白色钛金属", "storage": "128GB"}', 'ACTIVE'),
('var-4', 'prod-3', 'MI14-256GB-BLACK', '小米14 256GB 黑色', 3999.00, '{"color": "黑色", "storage": "256GB"}', 'ACTIVE'),
('var-5', 'prod-3', 'MI14-512GB-BLACK', '小米14 512GB 黑色', 4499.00, '{"color": "黑色", "storage": "512GB"}', 'ACTIVE');
