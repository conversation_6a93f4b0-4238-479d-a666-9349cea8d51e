package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pay-mall/services/product-service/internal/config"
	"pay-mall/services/product-service/internal/database"
	"pay-mall/services/product-service/internal/handler"
	"pay-mall/services/product-service/internal/repository"
	"pay-mall/services/product-service/internal/router"
	"pay-mall/services/product-service/internal/service"
)

// @title 商品服务API
// @version 1.0
// @description 商品服务的RESTful API文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	
	// 初始化数据库连接
	db, err := database.NewMySQLConnection(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to MySQL: %v", err)
	}
	defer db.Close()
	
	// 初始化Redis连接
	redisClient, err := database.NewRedisConnection(&cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()
	
	// 初始化事件发布器
	eventPublisher, err := service.NewEventPublisher(&cfg.Kafka)
	if err != nil {
		log.Fatalf("Failed to create event publisher: %v", err)
	}
	defer eventPublisher.Close()
	
	// 初始化Repository层
	categoryRepo := repository.NewCategoryRepository(db)
	productRepo := repository.NewProductRepository(db)
	productVariantRepo := repository.NewProductVariantRepository(db)
	
	// 初始化Service层
	cacheService := service.NewCacheService(redisClient, &cfg.Cache)
	categoryService := service.NewCategoryService(categoryRepo, cacheService)
	productService := service.NewProductService(productRepo, productVariantRepo, categoryRepo, cacheService, eventPublisher)
	
	// 初始化Handler层
	categoryHandler := handler.NewCategoryHandler(categoryService)
	productHandler := handler.NewProductHandler(productService)
	
	// 初始化路由
	r := router.NewRouter(categoryHandler, productHandler)
	engine := r.SetupRoutes()
	
	// 创建HTTP服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: engine,
	}
	
	// 启动服务器
	go func() {
		log.Printf("Starting product service on port %d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()
	
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	log.Println("Shutting down server...")
	
	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}
	
	log.Println("Server exited")
}
