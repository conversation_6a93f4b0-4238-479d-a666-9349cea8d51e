package database

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/redis/go-redis/v9"

	"pay-mall/services/product-service/internal/config"
)

// Database 数据库连接管理
type Database struct {
	MySQL *sql.DB
	Redis *redis.Client
}

// NewDatabase 创建数据库连接
func NewDatabase(cfg *config.Config) (*Database, error) {
	// 初始化MySQL连接
	mysql, err := initMySQL(&cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to init MySQL: %w", err)
	}

	// 初始化Redis连接
	redisClient := initRedis(&cfg.Redis)

	return &Database{
		MySQL: mysql,
		Redis: redisClient,
	}, nil
}

// initMySQL 初始化MySQL连接
func initMySQL(cfg *config.DatabaseConfig) (*sql.DB, error) {
	db, err := sql.Open("mysql", cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// 设置连接池参数
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping MySQL: %w", err)
	}

	return db, nil
}

// initRedis 初始化Redis连接
func initRedis(cfg *config.RedisConfig) *redis.Client {
	client := redis.NewClient(&redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Password,
		DB:           cfg.Database,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
	})

	return client
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	var err error

	if d.MySQL != nil {
		if mysqlErr := d.MySQL.Close(); mysqlErr != nil {
			err = fmt.Errorf("failed to close MySQL: %w", mysqlErr)
		}
	}

	if d.Redis != nil {
		if redisErr := d.Redis.Close(); redisErr != nil {
			if err != nil {
				err = fmt.Errorf("%v; failed to close Redis: %w", err, redisErr)
			} else {
				err = fmt.Errorf("failed to close Redis: %w", redisErr)
			}
		}
	}

	return err
}
