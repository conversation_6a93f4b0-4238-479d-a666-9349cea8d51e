package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"pay-mall/services/product-service/internal/model"
)

// ProductVariantRepository 商品变体数据访问接口
type ProductVariantRepository interface {
	Create(ctx context.Context, variant *model.ProductVariant) error
	GetByID(ctx context.Context, id string) (*model.ProductVariant, error)
	GetBySKU(ctx context.Context, sku string) (*model.ProductVariant, error)
	GetByProductID(ctx context.Context, productID string) ([]*model.ProductVariant, error)
	Update(ctx context.Context, id string, updates map[string]interface{}) error
	Delete(ctx context.Context, id string) error
	DeleteByProductID(ctx context.Context, productID string) error
}

// productVariantRepository 商品变体数据访问实现
type productVariantRepository struct {
	db *sql.DB
}

// NewProductVariantRepository 创建商品变体数据访问实例
func NewProductVariantRepository(db *sql.DB) ProductVariantRepository {
	return &productVariantRepository{db: db}
}

// Create 创建商品变体
func (r *productVariantRepository) Create(ctx context.Context, variant *model.ProductVariant) error {
	query := `
		INSERT INTO product_variants (id, product_id, sku, name, price, attributes, image_url, status, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	_, err := r.db.ExecContext(ctx, query,
		variant.ID, variant.ProductID, variant.SKU, variant.Name, variant.Price,
		variant.Attributes, variant.ImageURL, variant.Status, variant.CreatedAt, variant.UpdatedAt,
	)
	
	if err != nil {
		return fmt.Errorf("failed to create product variant: %w", err)
	}
	
	return nil
}

// GetByID 根据ID获取商品变体
func (r *productVariantRepository) GetByID(ctx context.Context, id string) (*model.ProductVariant, error) {
	query := `
		SELECT id, product_id, sku, name, price, attributes, image_url, status, created_at, updated_at
		FROM product_variants
		WHERE id = ?
	`
	
	variant := &model.ProductVariant{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&variant.ID, &variant.ProductID, &variant.SKU, &variant.Name, &variant.Price,
		&variant.Attributes, &variant.ImageURL, &variant.Status, &variant.CreatedAt, &variant.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product variant not found")
		}
		return nil, fmt.Errorf("failed to get product variant: %w", err)
	}
	
	return variant, nil
}

// GetBySKU 根据SKU获取商品变体
func (r *productVariantRepository) GetBySKU(ctx context.Context, sku string) (*model.ProductVariant, error) {
	query := `
		SELECT id, product_id, sku, name, price, attributes, image_url, status, created_at, updated_at
		FROM product_variants
		WHERE sku = ?
	`
	
	variant := &model.ProductVariant{}
	err := r.db.QueryRowContext(ctx, query, sku).Scan(
		&variant.ID, &variant.ProductID, &variant.SKU, &variant.Name, &variant.Price,
		&variant.Attributes, &variant.ImageURL, &variant.Status, &variant.CreatedAt, &variant.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product variant not found")
		}
		return nil, fmt.Errorf("failed to get product variant: %w", err)
	}
	
	return variant, nil
}

// GetByProductID 根据商品ID获取所有变体
func (r *productVariantRepository) GetByProductID(ctx context.Context, productID string) ([]*model.ProductVariant, error) {
	query := `
		SELECT id, product_id, sku, name, price, attributes, image_url, status, created_at, updated_at
		FROM product_variants
		WHERE product_id = ?
		ORDER BY created_at ASC
	`
	
	rows, err := r.db.QueryContext(ctx, query, productID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product variants: %w", err)
	}
	defer rows.Close()
	
	var variants []*model.ProductVariant
	for rows.Next() {
		variant := &model.ProductVariant{}
		err := rows.Scan(
			&variant.ID, &variant.ProductID, &variant.SKU, &variant.Name, &variant.Price,
			&variant.Attributes, &variant.ImageURL, &variant.Status, &variant.CreatedAt, &variant.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan product variant: %w", err)
		}
		variants = append(variants, variant)
	}
	
	return variants, nil
}

// Update 更新商品变体
func (r *productVariantRepository) Update(ctx context.Context, id string, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}
	
	var setParts []string
	var args []interface{}
	
	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = ?", field))
		args = append(args, value)
	}
	
	args = append(args, id)
	
	query := fmt.Sprintf(`
		UPDATE product_variants 
		SET %s, updated_at = NOW()
		WHERE id = ?
	`, strings.Join(setParts, ", "))
	
	result, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("failed to update product variant: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("product variant not found")
	}
	
	return nil
}

// Delete 删除商品变体
func (r *productVariantRepository) Delete(ctx context.Context, id string) error {
	query := "DELETE FROM product_variants WHERE id = ?"
	
	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete product variant: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("product variant not found")
	}
	
	return nil
}

// DeleteByProductID 删除商品的所有变体
func (r *productVariantRepository) DeleteByProductID(ctx context.Context, productID string) error {
	query := "DELETE FROM product_variants WHERE product_id = ?"
	
	_, err := r.db.ExecContext(ctx, query, productID)
	if err != nil {
		return fmt.Errorf("failed to delete product variants: %w", err)
	}
	
	return nil
}
