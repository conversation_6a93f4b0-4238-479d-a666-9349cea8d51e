package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"pay-mall/services/product-service/internal/model"
)

// CategoryRepository 分类数据访问接口
type CategoryRepository interface {
	Create(ctx context.Context, category *model.Category) error
	GetByID(ctx context.Context, id string) (*model.Category, error)
	GetByName(ctx context.Context, name string) (*model.Category, error)
	List(ctx context.Context, req *model.CategoryListRequest) ([]*model.Category, int, error)
	Update(ctx context.Context, id string, updates map[string]interface{}) error
	Delete(ctx context.Context, id string) error
	GetChildren(ctx context.Context, parentID string) ([]*model.Category, error)
	GetTree(ctx context.Context) ([]*model.Category, error)
	GetPath(ctx context.Context, id string) ([]*model.Category, error)
	UpdatePath(ctx context.Context, id, path string) error
	CountProducts(ctx context.Context, categoryID string) (int, error)
}

// categoryRepository 分类数据访问实现
type categoryRepository struct {
	db *sql.DB
}

// NewCategoryRepository 创建分类数据访问实例
func NewCategoryRepository(db *sql.DB) CategoryRepository {
	return &categoryRepository{db: db}
}

// Create 创建分类
func (r *categoryRepository) Create(ctx context.Context, category *model.Category) error {
	query := `
		INSERT INTO categories (id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	_, err := r.db.ExecContext(ctx, query,
		category.ID, category.Name, category.Description, category.ParentID,
		category.Level, category.Path, category.IconURL, category.SortOrder,
		category.Status, category.CreatedAt, category.UpdatedAt,
	)
	
	if err != nil {
		return fmt.Errorf("failed to create category: %w", err)
	}
	
	return nil
}

// GetByID 根据ID获取分类
func (r *categoryRepository) GetByID(ctx context.Context, id string) (*model.Category, error) {
	query := `
		SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
		FROM categories
		WHERE id = ?
	`
	
	category := &model.Category{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&category.ID, &category.Name, &category.Description, &category.ParentID,
		&category.Level, &category.Path, &category.IconURL, &category.SortOrder,
		&category.Status, &category.CreatedAt, &category.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	
	return category, nil
}

// GetByName 根据名称获取分类
func (r *categoryRepository) GetByName(ctx context.Context, name string) (*model.Category, error) {
	query := `
		SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
		FROM categories
		WHERE name = ?
	`
	
	category := &model.Category{}
	err := r.db.QueryRowContext(ctx, query, name).Scan(
		&category.ID, &category.Name, &category.Description, &category.ParentID,
		&category.Level, &category.Path, &category.IconURL, &category.SortOrder,
		&category.Status, &category.CreatedAt, &category.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	
	return category, nil
}

// List 获取分类列表
func (r *categoryRepository) List(ctx context.Context, req *model.CategoryListRequest) ([]*model.Category, int, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	
	if req.Level != nil {
		conditions = append(conditions, "level = ?")
		args = append(args, *req.Level)
	}
	
	if req.Status != nil {
		conditions = append(conditions, "status = ?")
		args = append(args, *req.Status)
	}
	
	if req.ParentID != nil {
		conditions = append(conditions, "parent_id = ?")
		args = append(args, *req.ParentID)
	}
	
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}
	
	// 计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM categories %s", whereClause)
	var total int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count categories: %w", err)
	}
	
	// 构建分页查询
	query := fmt.Sprintf(`
		SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
		FROM categories
		%s
		ORDER BY sort_order ASC, created_at DESC
		LIMIT ? OFFSET ?
	`, whereClause)
	
	offset := (req.Page - 1) * req.PageSize
	args = append(args, req.PageSize, offset)
	
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query categories: %w", err)
	}
	defer rows.Close()
	
	var categories []*model.Category
	for rows.Next() {
		category := &model.Category{}
		err := rows.Scan(
			&category.ID, &category.Name, &category.Description, &category.ParentID,
			&category.Level, &category.Path, &category.IconURL, &category.SortOrder,
			&category.Status, &category.CreatedAt, &category.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan category: %w", err)
		}
		categories = append(categories, category)
	}
	
	return categories, total, nil
}

// Update 更新分类
func (r *categoryRepository) Update(ctx context.Context, id string, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}
	
	var setParts []string
	var args []interface{}
	
	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = ?", field))
		args = append(args, value)
	}
	
	args = append(args, id)
	
	query := fmt.Sprintf(`
		UPDATE categories 
		SET %s, updated_at = NOW()
		WHERE id = ?
	`, strings.Join(setParts, ", "))
	
	result, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("failed to update category: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("category not found")
	}
	
	return nil
}

// Delete 删除分类
func (r *categoryRepository) Delete(ctx context.Context, id string) error {
	query := "DELETE FROM categories WHERE id = ?"
	
	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("category not found")
	}
	
	return nil
}

// GetChildren 获取子分类
func (r *categoryRepository) GetChildren(ctx context.Context, parentID string) ([]*model.Category, error) {
	query := `
		SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
		FROM categories
		WHERE parent_id = ?
		ORDER BY sort_order ASC, name ASC
	`
	
	rows, err := r.db.QueryContext(ctx, query, parentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get children categories: %w", err)
	}
	defer rows.Close()
	
	var categories []*model.Category
	for rows.Next() {
		category := &model.Category{}
		err := rows.Scan(
			&category.ID, &category.Name, &category.Description, &category.ParentID,
			&category.Level, &category.Path, &category.IconURL, &category.SortOrder,
			&category.Status, &category.CreatedAt, &category.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan category: %w", err)
		}
		categories = append(categories, category)
	}
	
	return categories, nil
}

// GetTree 获取分类树
func (r *categoryRepository) GetTree(ctx context.Context) ([]*model.Category, error) {
	query := `
		SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
		FROM categories
		WHERE status = 'ACTIVE'
		ORDER BY level ASC, sort_order ASC, name ASC
	`
	
	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get category tree: %w", err)
	}
	defer rows.Close()
	
	var categories []*model.Category
	for rows.Next() {
		category := &model.Category{}
		err := rows.Scan(
			&category.ID, &category.Name, &category.Description, &category.ParentID,
			&category.Level, &category.Path, &category.IconURL, &category.SortOrder,
			&category.Status, &category.CreatedAt, &category.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan category: %w", err)
		}
		categories = append(categories, category)
	}
	
	return categories, nil
}

// GetPath 获取分类路径
func (r *categoryRepository) GetPath(ctx context.Context, id string) ([]*model.Category, error) {
	// 递归查询分类路径
	query := `
		WITH RECURSIVE category_path AS (
			SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
			FROM categories
			WHERE id = ?
			
			UNION ALL
			
			SELECT c.id, c.name, c.description, c.parent_id, c.level, c.path, c.icon_url, c.sort_order, c.status, c.created_at, c.updated_at
			FROM categories c
			INNER JOIN category_path cp ON c.id = cp.parent_id
		)
		SELECT id, name, description, parent_id, level, path, icon_url, sort_order, status, created_at, updated_at
		FROM category_path
		ORDER BY level ASC
	`
	
	rows, err := r.db.QueryContext(ctx, query, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get category path: %w", err)
	}
	defer rows.Close()
	
	var categories []*model.Category
	for rows.Next() {
		category := &model.Category{}
		err := rows.Scan(
			&category.ID, &category.Name, &category.Description, &category.ParentID,
			&category.Level, &category.Path, &category.IconURL, &category.SortOrder,
			&category.Status, &category.CreatedAt, &category.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan category: %w", err)
		}
		categories = append(categories, category)
	}
	
	return categories, nil
}

// UpdatePath 更新分类路径
func (r *categoryRepository) UpdatePath(ctx context.Context, id, path string) error {
	query := "UPDATE categories SET path = ?, updated_at = NOW() WHERE id = ?"
	
	_, err := r.db.ExecContext(ctx, query, path, id)
	if err != nil {
		return fmt.Errorf("failed to update category path: %w", err)
	}
	
	return nil
}

// CountProducts 统计分类下的商品数量
func (r *categoryRepository) CountProducts(ctx context.Context, categoryID string) (int, error) {
	query := "SELECT COUNT(*) FROM products WHERE category_id = ? AND status != 'DELETED'"
	
	var count int
	err := r.db.QueryRowContext(ctx, query, categoryID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count products: %w", err)
	}
	
	return count, nil
}
