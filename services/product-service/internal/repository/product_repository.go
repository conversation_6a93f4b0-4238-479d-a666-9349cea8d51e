package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"pay-mall/services/product-service/internal/model"
)

// ProductRepository 商品数据访问接口
type ProductRepository interface {
	Create(ctx context.Context, product *model.Product) error
	GetByID(ctx context.Context, id string) (*model.Product, error)
	GetBySKU(ctx context.Context, sku string) (*model.Product, error)
	List(ctx context.Context, req *model.ProductListRequest) ([]*model.Product, int, error)
	Search(ctx context.Context, req *model.ProductSearchRequest) ([]*model.Product, int, error)
	Update(ctx context.Context, id string, updates map[string]interface{}) error
	Delete(ctx context.Context, id string) error
	UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error
	IncrementViewCount(ctx context.Context, id string) error
	IncrementSaleCount(ctx context.Context, id string, count int) error
	GetPopular(ctx context.Context, limit int) ([]*model.Product, error)
	GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error)
}

// productRepository 商品数据访问实现
type productRepository struct {
	db *sql.DB
}

// NewProductRepository 创建商品数据访问实例
func NewProductRepository(db *sql.DB) ProductRepository {
	return &productRepository{db: db}
}

// Create 创建商品
func (r *productRepository) Create(ctx context.Context, product *model.Product) error {
	query := `
		INSERT INTO products (
			id, name, description, price, original_price, image_url, image_urls,
			category_id, brand, sku, weight, dimensions, tags, attributes,
			status, sort_order, view_count, sale_count, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	_, err := r.db.ExecContext(ctx, query,
		product.ID, product.Name, product.Description, product.Price, product.OriginalPrice,
		product.ImageURL, product.ImageURLs, product.CategoryID, product.Brand, product.SKU,
		product.Weight, product.Dimensions, product.Tags, product.Attributes,
		product.Status, product.SortOrder, product.ViewCount, product.SaleCount,
		product.CreatedAt, product.UpdatedAt,
	)
	
	if err != nil {
		return fmt.Errorf("failed to create product: %w", err)
	}
	
	return nil
}

// GetByID 根据ID获取商品
func (r *productRepository) GetByID(ctx context.Context, id string) (*model.Product, error) {
	query := `
		SELECT id, name, description, price, original_price, image_url, image_urls,
			   category_id, brand, sku, weight, dimensions, tags, attributes,
			   status, sort_order, view_count, sale_count, created_at, updated_at
		FROM products
		WHERE id = ? AND status != 'DELETED'
	`
	
	product := &model.Product{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&product.ID, &product.Name, &product.Description, &product.Price, &product.OriginalPrice,
		&product.ImageURL, &product.ImageURLs, &product.CategoryID, &product.Brand, &product.SKU,
		&product.Weight, &product.Dimensions, &product.Tags, &product.Attributes,
		&product.Status, &product.SortOrder, &product.ViewCount, &product.SaleCount,
		&product.CreatedAt, &product.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product not found")
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}
	
	return product, nil
}

// GetBySKU 根据SKU获取商品
func (r *productRepository) GetBySKU(ctx context.Context, sku string) (*model.Product, error) {
	query := `
		SELECT id, name, description, price, original_price, image_url, image_urls,
			   category_id, brand, sku, weight, dimensions, tags, attributes,
			   status, sort_order, view_count, sale_count, created_at, updated_at
		FROM products
		WHERE sku = ? AND status != 'DELETED'
	`
	
	product := &model.Product{}
	err := r.db.QueryRowContext(ctx, query, sku).Scan(
		&product.ID, &product.Name, &product.Description, &product.Price, &product.OriginalPrice,
		&product.ImageURL, &product.ImageURLs, &product.CategoryID, &product.Brand, &product.SKU,
		&product.Weight, &product.Dimensions, &product.Tags, &product.Attributes,
		&product.Status, &product.SortOrder, &product.ViewCount, &product.SaleCount,
		&product.CreatedAt, &product.UpdatedAt,
	)
	
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product not found")
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}
	
	return product, nil
}

// List 获取商品列表
func (r *productRepository) List(ctx context.Context, req *model.ProductListRequest) ([]*model.Product, int, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	
	// 默认排除已删除的商品
	conditions = append(conditions, "status != 'DELETED'")
	
	if req.CategoryID != nil {
		conditions = append(conditions, "category_id = ?")
		args = append(args, *req.CategoryID)
	}
	
	if req.Status != nil {
		conditions = append(conditions, "status = ?")
		args = append(args, *req.Status)
	}
	
	if req.Brand != nil {
		conditions = append(conditions, "brand = ?")
		args = append(args, *req.Brand)
	}
	
	if req.MinPrice != nil {
		conditions = append(conditions, "price >= ?")
		args = append(args, *req.MinPrice)
	}
	
	if req.MaxPrice != nil {
		conditions = append(conditions, "price <= ?")
		args = append(args, *req.MaxPrice)
	}
	
	whereClause := "WHERE " + strings.Join(conditions, " AND ")
	
	// 计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM products %s", whereClause)
	var total int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count products: %w", err)
	}
	
	// 构建排序
	orderBy := "ORDER BY created_at DESC"
	if req.Sort != nil {
		direction := "DESC"
		if req.Order != nil && *req.Order == "asc" {
			direction = "ASC"
		}
		orderBy = fmt.Sprintf("ORDER BY %s %s", *req.Sort, direction)
	}
	
	// 构建分页查询
	query := fmt.Sprintf(`
		SELECT id, name, description, price, original_price, image_url, image_urls,
			   category_id, brand, sku, weight, dimensions, tags, attributes,
			   status, sort_order, view_count, sale_count, created_at, updated_at
		FROM products
		%s
		%s
		LIMIT ? OFFSET ?
	`, whereClause, orderBy)
	
	offset := (req.Page - 1) * req.PageSize
	args = append(args, req.PageSize, offset)
	
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query products: %w", err)
	}
	defer rows.Close()
	
	var products []*model.Product
	for rows.Next() {
		product := &model.Product{}
		err := rows.Scan(
			&product.ID, &product.Name, &product.Description, &product.Price, &product.OriginalPrice,
			&product.ImageURL, &product.ImageURLs, &product.CategoryID, &product.Brand, &product.SKU,
			&product.Weight, &product.Dimensions, &product.Tags, &product.Attributes,
			&product.Status, &product.SortOrder, &product.ViewCount, &product.SaleCount,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan product: %w", err)
		}
		products = append(products, product)
	}
	
	return products, total, nil
}

// Search 搜索商品
func (r *productRepository) Search(ctx context.Context, req *model.ProductSearchRequest) ([]*model.Product, int, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	
	// 默认排除已删除的商品
	conditions = append(conditions, "status != 'DELETED'")
	
	// 全文搜索
	conditions = append(conditions, "MATCH(name, description) AGAINST(? IN NATURAL LANGUAGE MODE)")
	args = append(args, req.Query)
	
	if req.CategoryID != nil {
		conditions = append(conditions, "category_id = ?")
		args = append(args, *req.CategoryID)
	}
	
	if req.Brand != nil {
		conditions = append(conditions, "brand = ?")
		args = append(args, *req.Brand)
	}
	
	if req.MinPrice != nil {
		conditions = append(conditions, "price >= ?")
		args = append(args, *req.MinPrice)
	}
	
	if req.MaxPrice != nil {
		conditions = append(conditions, "price <= ?")
		args = append(args, *req.MaxPrice)
	}
	
	whereClause := "WHERE " + strings.Join(conditions, " AND ")
	
	// 计算总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM products %s", whereClause)
	var total int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}
	
	// 构建排序
	orderBy := "ORDER BY MATCH(name, description) AGAINST(? IN NATURAL LANGUAGE MODE) DESC"
	searchArgs := append([]interface{}{req.Query}, args...)
	
	if req.Sort != nil {
		direction := "DESC"
		if req.Order != nil && *req.Order == "asc" {
			direction = "ASC"
		}
		
		switch *req.Sort {
		case "relevance":
			orderBy = "ORDER BY MATCH(name, description) AGAINST(? IN NATURAL LANGUAGE MODE) DESC"
		case "price", "view_count", "sale_count":
			orderBy = fmt.Sprintf("ORDER BY %s %s", *req.Sort, direction)
			searchArgs = args
		}
	}
	
	// 构建分页查询
	query := fmt.Sprintf(`
		SELECT id, name, description, price, original_price, image_url, image_urls,
			   category_id, brand, sku, weight, dimensions, tags, attributes,
			   status, sort_order, view_count, sale_count, created_at, updated_at
		FROM products
		%s
		%s
		LIMIT ? OFFSET ?
	`, whereClause, orderBy)
	
	offset := (req.Page - 1) * req.PageSize
	searchArgs = append(searchArgs, req.PageSize, offset)
	
	rows, err := r.db.QueryContext(ctx, query, searchArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search products: %w", err)
	}
	defer rows.Close()
	
	var products []*model.Product
	for rows.Next() {
		product := &model.Product{}
		err := rows.Scan(
			&product.ID, &product.Name, &product.Description, &product.Price, &product.OriginalPrice,
			&product.ImageURL, &product.ImageURLs, &product.CategoryID, &product.Brand, &product.SKU,
			&product.Weight, &product.Dimensions, &product.Tags, &product.Attributes,
			&product.Status, &product.SortOrder, &product.ViewCount, &product.SaleCount,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan product: %w", err)
		}
		products = append(products, product)
	}
	
	return products, total, nil
}

// Update 更新商品
func (r *productRepository) Update(ctx context.Context, id string, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}
	
	var setParts []string
	var args []interface{}
	
	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = ?", field))
		args = append(args, value)
	}
	
	args = append(args, id)
	
	query := fmt.Sprintf(`
		UPDATE products 
		SET %s, updated_at = NOW()
		WHERE id = ? AND status != 'DELETED'
	`, strings.Join(setParts, ", "))
	
	result, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("failed to update product: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("product not found")
	}
	
	return nil
}

// Delete 删除商品（软删除）
func (r *productRepository) Delete(ctx context.Context, id string) error {
	query := "UPDATE products SET status = 'DELETED', updated_at = NOW() WHERE id = ? AND status != 'DELETED'"
	
	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete product: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("product not found")
	}
	
	return nil
}

// UpdateStatus 更新商品状态
func (r *productRepository) UpdateStatus(ctx context.Context, id string, status model.ProductStatus) error {
	query := "UPDATE products SET status = ?, updated_at = NOW() WHERE id = ? AND status != 'DELETED'"
	
	result, err := r.db.ExecContext(ctx, query, status, id)
	if err != nil {
		return fmt.Errorf("failed to update product status: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("product not found")
	}
	
	return nil
}

// IncrementViewCount 增加浏览次数
func (r *productRepository) IncrementViewCount(ctx context.Context, id string) error {
	query := "UPDATE products SET view_count = view_count + 1, updated_at = NOW() WHERE id = ? AND status != 'DELETED'"
	
	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	
	return nil
}

// IncrementSaleCount 增加销售数量
func (r *productRepository) IncrementSaleCount(ctx context.Context, id string, count int) error {
	query := "UPDATE products SET sale_count = sale_count + ?, updated_at = NOW() WHERE id = ? AND status != 'DELETED'"
	
	_, err := r.db.ExecContext(ctx, query, count, id)
	if err != nil {
		return fmt.Errorf("failed to increment sale count: %w", err)
	}
	
	return nil
}

// GetPopular 获取热门商品
func (r *productRepository) GetPopular(ctx context.Context, limit int) ([]*model.Product, error) {
	query := `
		SELECT id, name, description, price, original_price, image_url, image_urls,
			   category_id, brand, sku, weight, dimensions, tags, attributes,
			   status, sort_order, view_count, sale_count, created_at, updated_at
		FROM products
		WHERE status = 'ACTIVE'
		ORDER BY sale_count DESC, view_count DESC
		LIMIT ?
	`
	
	rows, err := r.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular products: %w", err)
	}
	defer rows.Close()
	
	var products []*model.Product
	for rows.Next() {
		product := &model.Product{}
		err := rows.Scan(
			&product.ID, &product.Name, &product.Description, &product.Price, &product.OriginalPrice,
			&product.ImageURL, &product.ImageURLs, &product.CategoryID, &product.Brand, &product.SKU,
			&product.Weight, &product.Dimensions, &product.Tags, &product.Attributes,
			&product.Status, &product.SortOrder, &product.ViewCount, &product.SaleCount,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan product: %w", err)
		}
		products = append(products, product)
	}
	
	return products, nil
}

// GetByCategory 根据分类获取商品
func (r *productRepository) GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error) {
	query := `
		SELECT id, name, description, price, original_price, image_url, image_urls,
			   category_id, brand, sku, weight, dimensions, tags, attributes,
			   status, sort_order, view_count, sale_count, created_at, updated_at
		FROM products
		WHERE category_id = ? AND status = 'ACTIVE'
		ORDER BY sort_order ASC, created_at DESC
		LIMIT ?
	`
	
	rows, err := r.db.QueryContext(ctx, query, categoryID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get products by category: %w", err)
	}
	defer rows.Close()
	
	var products []*model.Product
	for rows.Next() {
		product := &model.Product{}
		err := rows.Scan(
			&product.ID, &product.Name, &product.Description, &product.Price, &product.OriginalPrice,
			&product.ImageURL, &product.ImageURLs, &product.CategoryID, &product.Brand, &product.SKU,
			&product.Weight, &product.Dimensions, &product.Tags, &product.Attributes,
			&product.Status, &product.SortOrder, &product.ViewCount, &product.SaleCount,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan product: %w", err)
		}
		products = append(products, product)
	}
	
	return products, nil
}
