package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"pay-mall/services/product-service/internal/config"
	"pay-mall/services/product-service/internal/model"
)

// CacheService 缓存服务接口
type CacheService interface {
	// 商品缓存
	SetProduct(ctx context.Context, product *model.Product) error
	GetProduct(ctx context.Context, id string) (*model.Product, error)
	DeleteProduct(ctx context.Context, id string) error
	
	// 分类缓存
	SetCategory(ctx context.Context, category *model.Category) error
	GetCategory(ctx context.Context, id string) (*model.Category, error)
	DeleteCategory(ctx context.Context, id string) error
	SetCategoryTree(ctx context.Context, tree []*model.Category) error
	GetCategoryTree(ctx context.Context) ([]*model.Category, error)
	DeleteCategoryTree(ctx context.Context) error
	
	// 商品列表缓存
	SetProductList(ctx context.Context, key string, products []*model.Product, total int) error
	GetProductList(ctx context.Context, key string) ([]*model.Product, int, error)
	DeleteProductList(ctx context.Context, pattern string) error
	
	// 搜索结果缓存
	SetSearchResult(ctx context.Context, key string, products []*model.Product, total int) error
	GetSearchResult(ctx context.Context, key string) ([]*model.Product, int, error)
	DeleteSearchResult(ctx context.Context, pattern string) error
}

// cacheService 缓存服务实现
type cacheService struct {
	redis  *redis.Client
	config *config.CacheConfig
}

// NewCacheService 创建缓存服务实例
func NewCacheService(redisClient *redis.Client, cfg *config.CacheConfig) CacheService {
	return &cacheService{
		redis:  redisClient,
		config: cfg,
	}
}

// 缓存键前缀
const (
	ProductKeyPrefix      = "product:"
	CategoryKeyPrefix     = "category:"
	CategoryTreeKey       = "category:tree"
	ProductListKeyPrefix  = "product:list:"
	SearchResultKeyPrefix = "search:result:"
)

// ProductListResult 商品列表缓存结果
type ProductListResult struct {
	Products []*model.Product `json:"products"`
	Total    int              `json:"total"`
}

// SetProduct 缓存商品
func (s *cacheService) SetProduct(ctx context.Context, product *model.Product) error {
	key := ProductKeyPrefix + product.ID
	data, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	
	err = s.redis.Set(ctx, key, data, s.config.GetProductDetailTTL()).Err()
	if err != nil {
		return fmt.Errorf("failed to cache product: %w", err)
	}
	
	return nil
}

// GetProduct 获取缓存的商品
func (s *cacheService) GetProduct(ctx context.Context, id string) (*model.Product, error) {
	key := ProductKeyPrefix + id
	data, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("failed to get cached product: %w", err)
	}
	
	var product model.Product
	err = json.Unmarshal([]byte(data), &product)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal product: %w", err)
	}
	
	return &product, nil
}

// DeleteProduct 删除商品缓存
func (s *cacheService) DeleteProduct(ctx context.Context, id string) error {
	key := ProductKeyPrefix + id
	err := s.redis.Del(ctx, key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete cached product: %w", err)
	}
	
	return nil
}

// SetCategory 缓存分类
func (s *cacheService) SetCategory(ctx context.Context, category *model.Category) error {
	key := CategoryKeyPrefix + category.ID
	data, err := json.Marshal(category)
	if err != nil {
		return fmt.Errorf("failed to marshal category: %w", err)
	}
	
	err = s.redis.Set(ctx, key, data, s.config.GetCategoryTreeTTL()).Err()
	if err != nil {
		return fmt.Errorf("failed to cache category: %w", err)
	}
	
	return nil
}

// GetCategory 获取缓存的分类
func (s *cacheService) GetCategory(ctx context.Context, id string) (*model.Category, error) {
	key := CategoryKeyPrefix + id
	data, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("failed to get cached category: %w", err)
	}
	
	var category model.Category
	err = json.Unmarshal([]byte(data), &category)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal category: %w", err)
	}
	
	return &category, nil
}

// DeleteCategory 删除分类缓存
func (s *cacheService) DeleteCategory(ctx context.Context, id string) error {
	key := CategoryKeyPrefix + id
	err := s.redis.Del(ctx, key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete cached category: %w", err)
	}
	
	return nil
}

// SetCategoryTree 缓存分类树
func (s *cacheService) SetCategoryTree(ctx context.Context, tree []*model.Category) error {
	data, err := json.Marshal(tree)
	if err != nil {
		return fmt.Errorf("failed to marshal category tree: %w", err)
	}
	
	err = s.redis.Set(ctx, CategoryTreeKey, data, s.config.GetCategoryTreeTTL()).Err()
	if err != nil {
		return fmt.Errorf("failed to cache category tree: %w", err)
	}
	
	return nil
}

// GetCategoryTree 获取缓存的分类树
func (s *cacheService) GetCategoryTree(ctx context.Context) ([]*model.Category, error) {
	data, err := s.redis.Get(ctx, CategoryTreeKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("failed to get cached category tree: %w", err)
	}
	
	var tree []*model.Category
	err = json.Unmarshal([]byte(data), &tree)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal category tree: %w", err)
	}
	
	return tree, nil
}

// DeleteCategoryTree 删除分类树缓存
func (s *cacheService) DeleteCategoryTree(ctx context.Context) error {
	err := s.redis.Del(ctx, CategoryTreeKey).Err()
	if err != nil {
		return fmt.Errorf("failed to delete cached category tree: %w", err)
	}
	
	return nil
}

// SetProductList 缓存商品列表
func (s *cacheService) SetProductList(ctx context.Context, key string, products []*model.Product, total int) error {
	cacheKey := ProductListKeyPrefix + key
	result := ProductListResult{
		Products: products,
		Total:    total,
	}
	
	data, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal product list: %w", err)
	}
	
	err = s.redis.Set(ctx, cacheKey, data, s.config.GetProductListTTL()).Err()
	if err != nil {
		return fmt.Errorf("failed to cache product list: %w", err)
	}
	
	return nil
}

// GetProductList 获取缓存的商品列表
func (s *cacheService) GetProductList(ctx context.Context, key string) ([]*model.Product, int, error) {
	cacheKey := ProductListKeyPrefix + key
	data, err := s.redis.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, 0, nil // 缓存未命中
		}
		return nil, 0, fmt.Errorf("failed to get cached product list: %w", err)
	}
	
	var result ProductListResult
	err = json.Unmarshal([]byte(data), &result)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to unmarshal product list: %w", err)
	}
	
	return result.Products, result.Total, nil
}

// DeleteProductList 删除商品列表缓存
func (s *cacheService) DeleteProductList(ctx context.Context, pattern string) error {
	keys, err := s.redis.Keys(ctx, ProductListKeyPrefix+pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get product list keys: %w", err)
	}
	
	if len(keys) > 0 {
		err = s.redis.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete product list cache: %w", err)
		}
	}
	
	return nil
}

// SetSearchResult 缓存搜索结果
func (s *cacheService) SetSearchResult(ctx context.Context, key string, products []*model.Product, total int) error {
	cacheKey := SearchResultKeyPrefix + key
	result := ProductListResult{
		Products: products,
		Total:    total,
	}
	
	data, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal search result: %w", err)
	}
	
	err = s.redis.Set(ctx, cacheKey, data, s.config.GetSearchResultTTL()).Err()
	if err != nil {
		return fmt.Errorf("failed to cache search result: %w", err)
	}
	
	return nil
}

// GetSearchResult 获取缓存的搜索结果
func (s *cacheService) GetSearchResult(ctx context.Context, key string) ([]*model.Product, int, error) {
	cacheKey := SearchResultKeyPrefix + key
	data, err := s.redis.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, 0, nil // 缓存未命中
		}
		return nil, 0, fmt.Errorf("failed to get cached search result: %w", err)
	}
	
	var result ProductListResult
	err = json.Unmarshal([]byte(data), &result)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to unmarshal search result: %w", err)
	}
	
	return result.Products, result.Total, nil
}

// DeleteSearchResult 删除搜索结果缓存
func (s *cacheService) DeleteSearchResult(ctx context.Context, pattern string) error {
	keys, err := s.redis.Keys(ctx, SearchResultKeyPrefix+pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get search result keys: %w", err)
	}
	
	if len(keys) > 0 {
		err = s.redis.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete search result cache: %w", err)
		}
	}
	
	return nil
}
