package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/repository"
)

// CategoryService 分类服务接口
type CategoryService interface {
	Create(ctx context.Context, req *model.CategoryCreateRequest) (*model.Category, error)
	GetByID(ctx context.Context, id string) (*model.Category, error)
	List(ctx context.Context, req *model.CategoryListRequest) ([]*model.Category, int, error)
	Update(ctx context.Context, id string, req *model.CategoryUpdateRequest) (*model.Category, error)
	Delete(ctx context.Context, id string) error
	GetTree(ctx context.Context) ([]*model.Category, error)
	GetChildren(ctx context.Context, parentID string) ([]*model.Category, error)
	GetPath(ctx context.Context, id string) ([]*model.Category, error)
	UpdateStatus(ctx context.Context, id string, status model.CategoryStatus) error
}

// categoryService 分类服务实现
type categoryService struct {
	categoryRepo repository.CategoryRepository
	cacheService CacheService
}

// NewCategoryService 创建分类服务实例
func NewCategoryService(categoryRepo repository.CategoryRepository, cacheService CacheService) CategoryService {
	return &categoryService{
		categoryRepo: categoryRepo,
		cacheService: cacheService,
	}
}

// Create 创建分类
func (s *categoryService) Create(ctx context.Context, req *model.CategoryCreateRequest) (*model.Category, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 检查名称是否已存在
	existing, err := s.categoryRepo.GetByName(ctx, req.Name)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("category name already exists")
	}

	// 计算层级和路径
	level := 1
	path := req.Name

	if req.ParentID != nil {
		parent, err := s.categoryRepo.GetByID(ctx, *req.ParentID)
		if err != nil {
			return nil, fmt.Errorf("parent category not found: %w", err)
		}

		level = parent.Level + 1
		path = parent.Path + "/" + req.Name
	}

	// 设置默认排序
	sortOrder := 0
	if req.SortOrder != nil {
		sortOrder = *req.SortOrder
	}

	// 创建分类
	category := &model.Category{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		ParentID:    req.ParentID,
		Level:       level,
		Path:        &path,
		IconURL:     req.IconURL,
		SortOrder:   sortOrder,
		Status:      model.CategoryStatusActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.categoryRepo.Create(ctx, category)
	if err != nil {
		return nil, fmt.Errorf("failed to create category: %w", err)
	}

	// 更新缓存
	_ = s.cacheService.SetCategory(ctx, category)
	_ = s.cacheService.DeleteCategoryTree(ctx)

	return category, nil
}

// GetByID 根据ID获取分类
func (s *categoryService) GetByID(ctx context.Context, id string) (*model.Category, error) {
	// 先从缓存获取
	category, err := s.cacheService.GetCategory(ctx, id)
	if err == nil && category != nil {
		return category, nil
	}

	// 从数据库获取
	category, err = s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// 更新缓存
	_ = s.cacheService.SetCategory(ctx, category)

	return category, nil
}

// List 获取分类列表
func (s *categoryService) List(ctx context.Context, req *model.CategoryListRequest) ([]*model.Category, int, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid request: %w", err)
	}

	categories, total, err := s.categoryRepo.List(ctx, req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list categories: %w", err)
	}

	return categories, total, nil
}

// Update 更新分类
func (s *categoryService) Update(ctx context.Context, id string, req *model.CategoryUpdateRequest) (*model.Category, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 检查分类是否存在
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("category not found: %w", err)
	}

	// 构建更新字段
	updates := make(map[string]interface{})

	if req.Name != nil && *req.Name != category.Name {
		// 检查新名称是否已存在
		existing, err := s.categoryRepo.GetByName(ctx, *req.Name)
		if err == nil && existing != nil && existing.ID != id {
			return nil, fmt.Errorf("category name already exists")
		}
		updates["name"] = *req.Name
		category.Name = *req.Name
	}

	if req.Description != nil {
		updates["description"] = *req.Description
		category.Description = req.Description
	}

	if req.IconURL != nil {
		updates["icon_url"] = *req.IconURL
		category.IconURL = req.IconURL
	}

	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
		category.SortOrder = *req.SortOrder
	}

	// 处理父分类变更
	if req.ParentID != nil {
		if category.ParentID == nil || *req.ParentID != *category.ParentID {
			// 验证新父分类
			if *req.ParentID != "" {
				parent, err := s.categoryRepo.GetByID(ctx, *req.ParentID)
				if err != nil {
					return nil, fmt.Errorf("parent category not found: %w", err)
				}

				// 检查是否会形成循环引用
				if parent.Path != nil && strings.Contains(*parent.Path, category.Name) {
					return nil, fmt.Errorf("circular reference detected")
				}

				category.Level = parent.Level + 1
				newPath := ""
				if parent.Path != nil {
					newPath = *parent.Path + "/" + category.Name
				} else {
					newPath = "/" + category.Name
				}
				category.Path = &newPath
			} else {
				category.Level = 1
				newPath := "/" + category.Name
				category.Path = &newPath
			}

			updates["parent_id"] = *req.ParentID
			updates["level"] = category.Level
			updates["path"] = category.Path
		}
	}

	if len(updates) == 0 {
		return category, nil
	}

	// 执行更新
	err = s.categoryRepo.Update(ctx, id, updates)
	if err != nil {
		return nil, fmt.Errorf("failed to update category: %w", err)
	}

	category.UpdatedAt = time.Now()

	// 更新缓存
	_ = s.cacheService.SetCategory(ctx, category)
	_ = s.cacheService.DeleteCategoryTree(ctx)

	return category, nil
}

// Delete 删除分类
func (s *categoryService) Delete(ctx context.Context, id string) error {
	// 检查分类是否存在
	_, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("category not found: %w", err)
	}

	// 检查是否有子分类
	children, err := s.categoryRepo.GetChildren(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check children: %w", err)
	}

	if len(children) > 0 {
		return fmt.Errorf("cannot delete category with children")
	}

	// 检查是否有商品
	productCount, err := s.categoryRepo.CountProducts(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to count products: %w", err)
	}

	if productCount > 0 {
		return fmt.Errorf("cannot delete category with products")
	}

	// 删除分类
	err = s.categoryRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}

	// 清除缓存
	_ = s.cacheService.DeleteCategory(ctx, id)
	_ = s.cacheService.DeleteCategoryTree(ctx)

	return nil
}

// GetTree 获取分类树
func (s *categoryService) GetTree(ctx context.Context) ([]*model.Category, error) {
	// 先从缓存获取
	tree, err := s.cacheService.GetCategoryTree(ctx)
	if err == nil && tree != nil {
		return tree, nil
	}

	// 从数据库获取
	tree, err = s.categoryRepo.GetTree(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get category tree: %w", err)
	}

	// 更新缓存
	_ = s.cacheService.SetCategoryTree(ctx, tree)

	return tree, nil
}

// GetChildren 获取子分类
func (s *categoryService) GetChildren(ctx context.Context, parentID string) ([]*model.Category, error) {
	children, err := s.categoryRepo.GetChildren(ctx, parentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get children: %w", err)
	}

	return children, nil
}

// GetPath 获取分类路径
func (s *categoryService) GetPath(ctx context.Context, id string) ([]*model.Category, error) {
	path, err := s.categoryRepo.GetPath(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get category path: %w", err)
	}

	return path, nil
}

// UpdateStatus 更新分类状态
func (s *categoryService) UpdateStatus(ctx context.Context, id string, status model.CategoryStatus) error {
	// 检查分类是否存在
	_, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("category not found: %w", err)
	}

	// 更新状态
	updates := map[string]interface{}{
		"status": status,
	}

	err = s.categoryRepo.Update(ctx, id, updates)
	if err != nil {
		return fmt.Errorf("failed to update category status: %w", err)
	}

	// 清除缓存
	_ = s.cacheService.DeleteCategory(ctx, id)
	_ = s.cacheService.DeleteCategoryTree(ctx)

	return nil
}
