package service

import (
	"context"
	"fmt"
	"log"

	"github.com/IBM/sarama"

	"pay-mall/services/product-service/internal/config"
	"pay-mall/services/product-service/internal/event"
	"pay-mall/services/product-service/internal/model"
)

// EventPublisher 事件发布器实现
type eventPublisher struct {
	producer sarama.SyncProducer
	config   *config.KafkaConfig
}

// NewEventPublisher 创建事件发布器实例
func NewEventPublisher(cfg *config.KafkaConfig) (event.EventPublisher, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 5
	config.Producer.Return.Successes = true
	
	producer, err := sarama.NewSyncProducer(cfg.Brokers, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka producer: %w", err)
	}
	
	return &eventPublisher{
		producer: producer,
		config:   cfg,
	}, nil
}

// PublishProductCreated 发布商品创建事件
func (p *eventPublisher) PublishProductCreated(product *model.Product) error {
	event := event.NewProductCreatedEvent(product)
	return p.publishEvent(p.config.Topics.ProductCreated, event)
}

// PublishProductUpdated 发布商品更新事件
func (p *eventPublisher) PublishProductUpdated(productID string, changes, previousData map[string]interface{}) error {
	event := event.NewProductUpdatedEvent(productID, changes, previousData)
	return p.publishEvent(p.config.Topics.ProductUpdated, event)
}

// PublishProductDeleted 发布商品删除事件
func (p *eventPublisher) PublishProductDeleted(product *model.Product) error {
	event := event.NewProductDeletedEvent(product)
	return p.publishEvent(p.config.Topics.ProductDeleted, event)
}

// PublishProductStatusChanged 发布商品状态变更事件
func (p *eventPublisher) PublishProductStatusChanged(productID string, previousStatus, newStatus model.ProductStatus, reason string) error {
	event := event.NewProductStatusChangedEvent(productID, previousStatus, newStatus, reason)
	return p.publishEvent(p.config.Topics.ProductStatusChanged, event)
}

// publishEvent 发布事件到Kafka
func (p *eventPublisher) publishEvent(topic string, evt interface{}) error {
	var data []byte
	var err error
	
	switch e := evt.(type) {
	case *event.ProductCreatedEvent:
		data, err = e.ToJSON()
	case *event.ProductUpdatedEvent:
		data, err = e.ToJSON()
	case *event.ProductDeletedEvent:
		data, err = e.ToJSON()
	case *event.ProductStatusChangedEvent:
		data, err = e.ToJSON()
	default:
		return fmt.Errorf("unsupported event type: %T", evt)
	}
	
	if err != nil {
		return fmt.Errorf("failed to serialize event: %w", err)
	}
	
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(data),
	}
	
	partition, offset, err := p.producer.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("failed to send message to Kafka: %w", err)
	}
	
	log.Printf("Event published to topic %s, partition %d, offset %d", topic, partition, offset)
	return nil
}

// Close 关闭事件发布器
func (p *eventPublisher) Close() error {
	return p.producer.Close()
}

// EventSubscriber 事件订阅器接口
type EventSubscriber interface {
	Subscribe(ctx context.Context, topics []string, handler EventHandler) error
	Close() error
}

// EventHandler 事件处理器接口
type EventHandler interface {
	HandleProductCreated(ctx context.Context, event *event.ProductCreatedEvent) error
	HandleProductUpdated(ctx context.Context, event *event.ProductUpdatedEvent) error
	HandleProductDeleted(ctx context.Context, event *event.ProductDeletedEvent) error
	HandleProductStatusChanged(ctx context.Context, event *event.ProductStatusChangedEvent) error
}

// eventSubscriber 事件订阅器实现
type eventSubscriber struct {
	consumer sarama.ConsumerGroup
	config   *config.KafkaConfig
}

// NewEventSubscriber 创建事件订阅器实例
func NewEventSubscriber(cfg *config.KafkaConfig, groupID string) (EventSubscriber, error) {
	config := sarama.NewConfig()
	config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
	config.Consumer.Offsets.Initial = sarama.OffsetNewest
	
	consumer, err := sarama.NewConsumerGroup(cfg.Brokers, groupID, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka consumer group: %w", err)
	}
	
	return &eventSubscriber{
		consumer: consumer,
		config:   cfg,
	}, nil
}

// Subscribe 订阅事件
func (s *eventSubscriber) Subscribe(ctx context.Context, topics []string, handler EventHandler) error {
	consumerHandler := &consumerGroupHandler{
		handler: handler,
	}
	
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			err := s.consumer.Consume(ctx, topics, consumerHandler)
			if err != nil {
				log.Printf("Error consuming messages: %v", err)
				return err
			}
		}
	}
}

// Close 关闭事件订阅器
func (s *eventSubscriber) Close() error {
	return s.consumer.Close()
}

// consumerGroupHandler 消费者组处理器
type consumerGroupHandler struct {
	handler EventHandler
}

// Setup 设置消费者组
func (h *consumerGroupHandler) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup 清理消费者组
func (h *consumerGroupHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 消费消息
func (h *consumerGroupHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message := <-claim.Messages():
			if message == nil {
				return nil
			}
			
			err := h.handleMessage(session.Context(), message)
			if err != nil {
				log.Printf("Error handling message: %v", err)
				// 根据业务需求决定是否继续处理或返回错误
				continue
			}
			
			session.MarkMessage(message, "")
			
		case <-session.Context().Done():
			return nil
		}
	}
}

// handleMessage 处理消息
func (h *consumerGroupHandler) handleMessage(ctx context.Context, message *sarama.ConsumerMessage) error {
	switch message.Topic {
	case "product.created":
		var evt event.ProductCreatedEvent
		if err := evt.UnmarshalJSON(message.Value); err != nil {
			return fmt.Errorf("failed to unmarshal ProductCreatedEvent: %w", err)
		}
		return h.handler.HandleProductCreated(ctx, &evt)
		
	case "product.updated":
		var evt event.ProductUpdatedEvent
		if err := evt.UnmarshalJSON(message.Value); err != nil {
			return fmt.Errorf("failed to unmarshal ProductUpdatedEvent: %w", err)
		}
		return h.handler.HandleProductUpdated(ctx, &evt)
		
	case "product.deleted":
		var evt event.ProductDeletedEvent
		if err := evt.UnmarshalJSON(message.Value); err != nil {
			return fmt.Errorf("failed to unmarshal ProductDeletedEvent: %w", err)
		}
		return h.handler.HandleProductDeleted(ctx, &evt)
		
	case "product.status.changed":
		var evt event.ProductStatusChangedEvent
		if err := evt.UnmarshalJSON(message.Value); err != nil {
			return fmt.Errorf("failed to unmarshal ProductStatusChangedEvent: %w", err)
		}
		return h.handler.HandleProductStatusChanged(ctx, &evt)
		
	default:
		log.Printf("Unknown topic: %s", message.Topic)
		return nil
	}
}
