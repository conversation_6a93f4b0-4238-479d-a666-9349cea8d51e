package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"github.com/google/uuid"

	"pay-mall/services/product-service/internal/event"
	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/repository"
)

// ProductService 商品服务接口
type ProductService interface {
	Create(ctx context.Context, req *model.ProductCreateRequest) (*model.Product, error)
	GetByID(ctx context.Context, id string) (*model.Product, error)
	GetBySKU(ctx context.Context, sku string) (*model.Product, error)
	List(ctx context.Context, req *model.ProductListRequest) ([]*model.Product, int, error)
	Search(ctx context.Context, req *model.ProductSearchRequest) ([]*model.Product, int, error)
	Update(ctx context.Context, id string, req *model.ProductUpdateRequest) (*model.Product, error)
	Delete(ctx context.Context, id string) error
	UpdateStatus(ctx context.Context, id string, status model.ProductStatus, reason string) error
	IncrementViewCount(ctx context.Context, id string) error
	GetPopular(ctx context.Context, limit int) ([]*model.Product, error)
	GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error)
}

// productService 商品服务实现
type productService struct {
	productRepo        repository.ProductRepository
	productVariantRepo repository.ProductVariantRepository
	categoryRepo       repository.CategoryRepository
	cacheService       CacheService
	eventPublisher     event.EventPublisher
}

// NewProductService 创建商品服务实例
func NewProductService(
	productRepo repository.ProductRepository,
	productVariantRepo repository.ProductVariantRepository,
	categoryRepo repository.CategoryRepository,
	cacheService CacheService,
	eventPublisher event.EventPublisher,
) ProductService {
	return &productService{
		productRepo:        productRepo,
		productVariantRepo: productVariantRepo,
		categoryRepo:       categoryRepo,
		cacheService:       cacheService,
		eventPublisher:     eventPublisher,
	}
}

// Create 创建商品
func (s *productService) Create(ctx context.Context, req *model.ProductCreateRequest) (*model.Product, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}
	
	// 验证分类是否存在
	_, err := s.categoryRepo.GetByID(ctx, req.CategoryID)
	if err != nil {
		return nil, fmt.Errorf("category not found: %w", err)
	}
	
	// 检查SKU是否已存在
	if req.SKU != nil {
		existing, err := s.productRepo.GetBySKU(ctx, *req.SKU)
		if err == nil && existing != nil {
			return nil, fmt.Errorf("SKU already exists")
		}
	}
	
	// 创建商品
	product := &model.Product{
		ID:            uuid.New().String(),
		Name:          req.Name,
		Description:   req.Description,
		Price:         req.Price,
		OriginalPrice: req.OriginalPrice,
		ImageURL:      req.ImageURL,
		ImageURLs:     req.ImageURLs,
		CategoryID:    req.CategoryID,
		Brand:         req.Brand,
		SKU:           req.SKU,
		Weight:        req.Weight,
		Dimensions:    req.Dimensions,
		Tags:          req.Tags,
		Attributes:    req.Attributes,
		Status:        model.ProductStatusDraft,
		SortOrder:     req.SortOrder,
		ViewCount:     0,
		SaleCount:     0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	
	err = s.productRepo.Create(ctx, product)
	if err != nil {
		return nil, fmt.Errorf("failed to create product: %w", err)
	}
	
	// 发布事件
	_ = s.eventPublisher.PublishProductCreated(product)
	
	// 更新缓存
	_ = s.cacheService.SetProduct(ctx, product)
	
	return product, nil
}

// GetByID 根据ID获取商品
func (s *productService) GetByID(ctx context.Context, id string) (*model.Product, error) {
	// 先从缓存获取
	product, err := s.cacheService.GetProduct(ctx, id)
	if err == nil && product != nil {
		return product, nil
	}
	
	// 从数据库获取
	product, err = s.productRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get product: %w", err)
	}
	
	// 更新缓存
	_ = s.cacheService.SetProduct(ctx, product)
	
	return product, nil
}

// GetBySKU 根据SKU获取商品
func (s *productService) GetBySKU(ctx context.Context, sku string) (*model.Product, error) {
	product, err := s.productRepo.GetBySKU(ctx, sku)
	if err != nil {
		return nil, fmt.Errorf("failed to get product by SKU: %w", err)
	}
	
	return product, nil
}

// List 获取商品列表
func (s *productService) List(ctx context.Context, req *model.ProductListRequest) ([]*model.Product, int, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid request: %w", err)
	}
	
	// 生成缓存键
	cacheKey := s.generateListCacheKey(req)
	
	// 先从缓存获取
	products, total, err := s.cacheService.GetProductList(ctx, cacheKey)
	if err == nil && products != nil {
		return products, total, nil
	}
	
	// 从数据库获取
	products, total, err = s.productRepo.List(ctx, req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}
	
	// 更新缓存
	_ = s.cacheService.SetProductList(ctx, cacheKey, products, total)
	
	return products, total, nil
}

// Search 搜索商品
func (s *productService) Search(ctx context.Context, req *model.ProductSearchRequest) ([]*model.Product, int, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid request: %w", err)
	}
	
	// 生成缓存键
	cacheKey := s.generateSearchCacheKey(req)
	
	// 先从缓存获取
	products, total, err := s.cacheService.GetSearchResult(ctx, cacheKey)
	if err == nil && products != nil {
		return products, total, nil
	}
	
	// 从数据库搜索
	products, total, err = s.productRepo.Search(ctx, req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search products: %w", err)
	}
	
	// 更新缓存
	_ = s.cacheService.SetSearchResult(ctx, cacheKey, products, total)
	
	return products, total, nil
}

// Update 更新商品
func (s *productService) Update(ctx context.Context, id string, req *model.ProductUpdateRequest) (*model.Product, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}
	
	// 获取原商品信息
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("product not found: %w", err)
	}
	
	// 记录变更前的数据
	previousData := make(map[string]interface{})
	changes := make(map[string]interface{})
	
	// 构建更新字段
	updates := make(map[string]interface{})
	
	if req.Name != nil && *req.Name != product.Name {
		previousData["name"] = product.Name
		changes["name"] = *req.Name
		updates["name"] = *req.Name
		product.Name = *req.Name
	}
	
	if req.Description != nil && *req.Description != *product.Description {
		previousData["description"] = product.Description
		changes["description"] = *req.Description
		updates["description"] = *req.Description
		product.Description = req.Description
	}
	
	if req.Price != nil && *req.Price != product.Price {
		previousData["price"] = product.Price
		changes["price"] = *req.Price
		updates["price"] = *req.Price
		product.Price = *req.Price
	}
	
	if req.OriginalPrice != nil && *req.OriginalPrice != *product.OriginalPrice {
		previousData["original_price"] = product.OriginalPrice
		changes["original_price"] = *req.OriginalPrice
		updates["original_price"] = *req.OriginalPrice
		product.OriginalPrice = req.OriginalPrice
	}
	
	if req.ImageURL != nil && *req.ImageURL != *product.ImageURL {
		previousData["image_url"] = product.ImageURL
		changes["image_url"] = *req.ImageURL
		updates["image_url"] = *req.ImageURL
		product.ImageURL = req.ImageURL
	}
	
	if req.CategoryID != nil && *req.CategoryID != product.CategoryID {
		// 验证新分类是否存在
		_, err := s.categoryRepo.GetByID(ctx, *req.CategoryID)
		if err != nil {
			return nil, fmt.Errorf("category not found: %w", err)
		}
		
		previousData["category_id"] = product.CategoryID
		changes["category_id"] = *req.CategoryID
		updates["category_id"] = *req.CategoryID
		product.CategoryID = *req.CategoryID
	}
	
	if req.SKU != nil && (product.SKU == nil || *req.SKU != *product.SKU) {
		// 检查新SKU是否已存在
		existing, err := s.productRepo.GetBySKU(ctx, *req.SKU)
		if err == nil && existing != nil && existing.ID != id {
			return nil, fmt.Errorf("SKU already exists")
		}
		
		previousData["sku"] = product.SKU
		changes["sku"] = *req.SKU
		updates["sku"] = *req.SKU
		product.SKU = req.SKU
	}
	
	if req.Brand != nil {
		previousData["brand"] = product.Brand
		changes["brand"] = *req.Brand
		updates["brand"] = *req.Brand
		product.Brand = req.Brand
	}
	
	if req.Weight != nil {
		previousData["weight"] = product.Weight
		changes["weight"] = *req.Weight
		updates["weight"] = *req.Weight
		product.Weight = req.Weight
	}
	
	if req.SortOrder != nil && *req.SortOrder != product.SortOrder {
		previousData["sort_order"] = product.SortOrder
		changes["sort_order"] = *req.SortOrder
		updates["sort_order"] = *req.SortOrder
		product.SortOrder = *req.SortOrder
	}
	
	if len(updates) == 0 {
		return product, nil
	}
	
	// 执行更新
	err = s.productRepo.Update(ctx, id, updates)
	if err != nil {
		return nil, fmt.Errorf("failed to update product: %w", err)
	}
	
	product.UpdatedAt = time.Now()
	
	// 发布事件
	_ = s.eventPublisher.PublishProductUpdated(id, changes, previousData)
	
	// 更新缓存
	_ = s.cacheService.SetProduct(ctx, product)
	_ = s.cacheService.DeleteProductList(ctx, "*")
	_ = s.cacheService.DeleteSearchResult(ctx, "*")
	
	return product, nil
}

// Delete 删除商品
func (s *productService) Delete(ctx context.Context, id string) error {
	// 获取商品信息
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("product not found: %w", err)
	}
	
	// 删除商品
	err = s.productRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete product: %w", err)
	}
	
	// 删除商品变体
	_ = s.productVariantRepo.DeleteByProductID(ctx, id)
	
	// 发布事件
	_ = s.eventPublisher.PublishProductDeleted(product)
	
	// 清除缓存
	_ = s.cacheService.DeleteProduct(ctx, id)
	_ = s.cacheService.DeleteProductList(ctx, "*")
	_ = s.cacheService.DeleteSearchResult(ctx, "*")
	
	return nil
}

// UpdateStatus 更新商品状态
func (s *productService) UpdateStatus(ctx context.Context, id string, status model.ProductStatus, reason string) error {
	// 获取原商品信息
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("product not found: %w", err)
	}
	
	previousStatus := product.Status
	
	// 更新状态
	err = s.productRepo.UpdateStatus(ctx, id, status)
	if err != nil {
		return fmt.Errorf("failed to update product status: %w", err)
	}
	
	// 发布事件
	_ = s.eventPublisher.PublishProductStatusChanged(id, previousStatus, status, reason)
	
	// 清除缓存
	_ = s.cacheService.DeleteProduct(ctx, id)
	_ = s.cacheService.DeleteProductList(ctx, "*")
	_ = s.cacheService.DeleteSearchResult(ctx, "*")
	
	return nil
}

// IncrementViewCount 增加浏览次数
func (s *productService) IncrementViewCount(ctx context.Context, id string) error {
	err := s.productRepo.IncrementViewCount(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	
	// 清除缓存
	_ = s.cacheService.DeleteProduct(ctx, id)
	
	return nil
}

// GetPopular 获取热门商品
func (s *productService) GetPopular(ctx context.Context, limit int) ([]*model.Product, error) {
	products, err := s.productRepo.GetPopular(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular products: %w", err)
	}
	
	return products, nil
}

// GetByCategory 根据分类获取商品
func (s *productService) GetByCategory(ctx context.Context, categoryID string, limit int) ([]*model.Product, error) {
	products, err := s.productRepo.GetByCategory(ctx, categoryID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get products by category: %w", err)
	}
	
	return products, nil
}

// generateListCacheKey 生成列表缓存键
func (s *productService) generateListCacheKey(req *model.ProductListRequest) string {
	key := fmt.Sprintf("list:%d:%d", req.Page, req.PageSize)
	
	if req.CategoryID != nil {
		key += fmt.Sprintf(":cat:%s", *req.CategoryID)
	}
	if req.Status != nil {
		key += fmt.Sprintf(":status:%s", *req.Status)
	}
	if req.Brand != nil {
		key += fmt.Sprintf(":brand:%s", *req.Brand)
	}
	if req.MinPrice != nil {
		key += fmt.Sprintf(":minprice:%.2f", *req.MinPrice)
	}
	if req.MaxPrice != nil {
		key += fmt.Sprintf(":maxprice:%.2f", *req.MaxPrice)
	}
	if req.Sort != nil {
		key += fmt.Sprintf(":sort:%s", *req.Sort)
	}
	if req.Order != nil {
		key += fmt.Sprintf(":order:%s", *req.Order)
	}
	
	// 使用MD5哈希缩短键长度
	hash := md5.Sum([]byte(key))
	return fmt.Sprintf("%x", hash)
}

// generateSearchCacheKey 生成搜索缓存键
func (s *productService) generateSearchCacheKey(req *model.ProductSearchRequest) string {
	key := fmt.Sprintf("search:%s:%d:%d", req.Query, req.Page, req.PageSize)
	
	if req.CategoryID != nil {
		key += fmt.Sprintf(":cat:%s", *req.CategoryID)
	}
	if req.Brand != nil {
		key += fmt.Sprintf(":brand:%s", *req.Brand)
	}
	if req.MinPrice != nil {
		key += fmt.Sprintf(":minprice:%.2f", *req.MinPrice)
	}
	if req.MaxPrice != nil {
		key += fmt.Sprintf(":maxprice:%.2f", *req.MaxPrice)
	}
	if req.Sort != nil {
		key += fmt.Sprintf(":sort:%s", *req.Sort)
	}
	if req.Order != nil {
		key += fmt.Sprintf(":order:%s", *req.Order)
	}
	
	// 使用MD5哈希缩短键长度
	hash := md5.Sum([]byte(key))
	return fmt.Sprintf("%x", hash)
}
