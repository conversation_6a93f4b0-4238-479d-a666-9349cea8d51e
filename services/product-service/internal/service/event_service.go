package service

import (
	"pay-mall/pkg/kafka"
	"pay-mall/services/product-service/internal/model"
)

// EventService 事件服务接口
type EventService interface {
	PublishProductCreated(product *model.Product) error
	PublishProductUpdated(productID string, changes map[string]interface{}) error
	PublishProductDeleted(productID string) error
	PublishProductStatusChanged(productID string, oldStatus, newStatus model.ProductStatus) error
	PublishCategoryCreated(category *model.Category) error
	PublishCategoryUpdated(categoryID string, changes map[string]interface{}) error
	PublishCategoryDeleted(categoryID string) error
	Close() error
}

// eventService 事件服务实现
type eventService struct {
	publisher *kafka.KafkaEventPublisher
}

// NewEventService 创建事件服务实例
func NewEventService(brokers []string, topic string) (EventService, error) {
	publisher, err := kafka.NewKafkaEventPublisher(brokers, topic, "product-service")
	if err != nil {
		return nil, err
	}

	return &eventService{
		publisher: publisher,
	}, nil
}

// PublishProductCreated 发布商品创建事件
func (s *eventService) PublishProductCreated(product *model.Product) error {
	data := map[string]interface{}{
		"product_id":   product.ID,
		"name":         product.Name,
		"category_id":  product.CategoryID,
		"price":        product.Price,
		"status":       product.Status,
		"created_at":   product.CreatedAt,
	}
	return s.publisher.PublishInventoryEvent("ProductCreated", data)
}

// PublishProductUpdated 发布商品更新事件
func (s *eventService) PublishProductUpdated(productID string, changes map[string]interface{}) error {
	data := map[string]interface{}{
		"product_id": productID,
		"changes":    changes,
	}
	return s.publisher.PublishInventoryEvent("ProductUpdated", data)
}

// PublishProductDeleted 发布商品删除事件
func (s *eventService) PublishProductDeleted(productID string) error {
	data := map[string]interface{}{
		"product_id": productID,
	}
	return s.publisher.PublishInventoryEvent("ProductDeleted", data)
}

// PublishProductStatusChanged 发布商品状态变更事件
func (s *eventService) PublishProductStatusChanged(productID string, oldStatus, newStatus model.ProductStatus) error {
	data := map[string]interface{}{
		"product_id":  productID,
		"old_status": oldStatus,
		"new_status": newStatus,
	}
	return s.publisher.PublishInventoryEvent("ProductStatusChanged", data)
}

// PublishCategoryCreated 发布分类创建事件
func (s *eventService) PublishCategoryCreated(category *model.Category) error {
	data := map[string]interface{}{
		"category_id": category.ID,
		"name":        category.Name,
		"parent_id":   category.ParentID,
		"level":       category.Level,
		"created_at":  category.CreatedAt,
	}
	return s.publisher.PublishInventoryEvent("CategoryCreated", data)
}

// PublishCategoryUpdated 发布分类更新事件
func (s *eventService) PublishCategoryUpdated(categoryID string, changes map[string]interface{}) error {
	data := map[string]interface{}{
		"category_id": categoryID,
		"changes":     changes,
	}
	return s.publisher.PublishInventoryEvent("CategoryUpdated", data)
}

// PublishCategoryDeleted 发布分类删除事件
func (s *eventService) PublishCategoryDeleted(categoryID string) error {
	data := map[string]interface{}{
		"category_id": categoryID,
	}
	return s.publisher.PublishInventoryEvent("CategoryDeleted", data)
}

// Close 关闭事件发布器
func (s *eventService) Close() error {
	return s.publisher.Close()
}
