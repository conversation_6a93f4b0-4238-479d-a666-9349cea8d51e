package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"pay-mall/services/product-service/internal/model"
	"pay-mall/services/product-service/internal/service"
)

// CategoryHandler 分类处理器
type CategoryHandler struct {
	categoryService service.CategoryService
}

// NewCategoryHandler 创建分类处理器实例
func NewCategoryHandler(categoryService service.CategoryService) *CategoryHandler {
	return &CategoryHandler{
		categoryService: categoryService,
	}
}

// Create 创建分类
// @Summary 创建分类
// @Description 创建新的商品分类
// @Tags categories
// @Accept json
// @Produce json
// @Param category body model.CategoryCreateRequest true "分类信息"
// @Success 201 {object} model.CategoryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories [post]
func (h *CategoryHandler) Create(c *gin.Context) {
	var req model.CategoryCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: err.Error(),
		})
		return
	}
	
	category, err := h.categoryService.Create(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "CREATE_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, model.CategoryResponse{
		Category: category,
	})
}

// GetByID 根据ID获取分类
// @Summary 获取分类详情
// @Description 根据ID获取分类详细信息
// @Tags categories
// @Produce json
// @Param id path string true "分类ID"
// @Success 200 {object} model.CategoryResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/{id} [get]
func (h *CategoryHandler) GetByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Category ID is required",
		})
		return
	}
	
	category, err := h.categoryService.GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Code:    "CATEGORY_NOT_FOUND",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.CategoryResponse{
		Category: category,
	})
}

// List 获取分类列表
// @Summary 获取分类列表
// @Description 分页获取分类列表
// @Tags categories
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param level query int false "分类层级"
// @Param status query string false "分类状态"
// @Param parent_id query string false "父分类ID"
// @Success 200 {object} model.CategoryListResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories [get]
func (h *CategoryHandler) List(c *gin.Context) {
	req := &model.CategoryListRequest{
		Page:     1,
		PageSize: 20,
	}
	
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}
	
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			req.PageSize = ps
		}
	}
	
	if level := c.Query("level"); level != "" {
		if l, err := strconv.Atoi(level); err == nil {
			req.Level = &l
		}
	}
	
	if status := c.Query("status"); status != "" {
		categoryStatus := model.CategoryStatus(status)
		req.Status = &categoryStatus
	}
	
	if parentID := c.Query("parent_id"); parentID != "" {
		req.ParentID = &parentID
	}
	
	categories, total, err := h.categoryService.List(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "LIST_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.CategoryListResponse{
		Categories: categories,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
	})
}

// Update 更新分类
// @Summary 更新分类
// @Description 更新分类信息
// @Tags categories
// @Accept json
// @Produce json
// @Param id path string true "分类ID"
// @Param category body model.CategoryUpdateRequest true "更新信息"
// @Success 200 {object} model.CategoryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/{id} [put]
func (h *CategoryHandler) Update(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Category ID is required",
		})
		return
	}
	
	var req model.CategoryUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: err.Error(),
		})
		return
	}
	
	category, err := h.categoryService.Update(c.Request.Context(), id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.CategoryResponse{
		Category: category,
	})
}

// Delete 删除分类
// @Summary 删除分类
// @Description 删除指定分类
// @Tags categories
// @Produce json
// @Param id path string true "分类ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/{id} [delete]
func (h *CategoryHandler) Delete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Category ID is required",
		})
		return
	}
	
	err := h.categoryService.Delete(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DELETE_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.Status(http.StatusNoContent)
}

// GetTree 获取分类树
// @Summary 获取分类树
// @Description 获取完整的分类树结构
// @Tags categories
// @Produce json
// @Success 200 {object} model.CategoryTreeResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/tree [get]
func (h *CategoryHandler) GetTree(c *gin.Context) {
	tree, err := h.categoryService.GetTree(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_TREE_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.CategoryTreeResponse{
		Tree: tree,
	})
}

// GetChildren 获取子分类
// @Summary 获取子分类
// @Description 获取指定分类的所有子分类
// @Tags categories
// @Produce json
// @Param id path string true "父分类ID"
// @Success 200 {object} model.CategoryListResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/{id}/children [get]
func (h *CategoryHandler) GetChildren(c *gin.Context) {
	parentID := c.Param("id")
	if parentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Parent category ID is required",
		})
		return
	}
	
	children, err := h.categoryService.GetChildren(c.Request.Context(), parentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_CHILDREN_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.CategoryListResponse{
		Categories: children,
		Total:      len(children),
		Page:       1,
		PageSize:   len(children),
	})
}

// GetPath 获取分类路径
// @Summary 获取分类路径
// @Description 获取从根分类到指定分类的完整路径
// @Tags categories
// @Produce json
// @Param id path string true "分类ID"
// @Success 200 {object} model.CategoryPathResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/{id}/path [get]
func (h *CategoryHandler) GetPath(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Category ID is required",
		})
		return
	}
	
	path, err := h.categoryService.GetPath(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "GET_PATH_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, model.CategoryPathResponse{
		Path: path,
	})
}

// UpdateStatus 更新分类状态
// @Summary 更新分类状态
// @Description 更新分类的状态
// @Tags categories
// @Accept json
// @Produce json
// @Param id path string true "分类ID"
// @Param status body model.CategoryStatusUpdateRequest true "状态信息"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /categories/{id}/status [patch]
func (h *CategoryHandler) UpdateStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_ID",
			Message: "Category ID is required",
		})
		return
	}
	
	var req model.CategoryStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: err.Error(),
		})
		return
	}
	
	err := h.categoryService.UpdateStatus(c.Request.Context(), id, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "UPDATE_STATUS_FAILED",
			Message: err.Error(),
		})
		return
	}
	
	c.Status(http.StatusNoContent)
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}
