package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// CategoryStatus 分类状态
type CategoryStatus string

const (
	CategoryStatusActive   CategoryStatus = "ACTIVE"
	CategoryStatusInactive CategoryStatus = "INACTIVE"
)

// Category 商品分类模型
type Category struct {
	ID          string         `json:"id" db:"id"`
	Name        string         `json:"name" db:"name"`
	Description *string        `json:"description" db:"description"`
	ParentID    *string        `json:"parent_id" db:"parent_id"`
	Level       int            `json:"level" db:"level"`
	Path        *string        `json:"path" db:"path"`
	IconURL     *string        `json:"icon_url" db:"icon_url"`
	SortOrder   int            `json:"sort_order" db:"sort_order"`
	Status      CategoryStatus `json:"status" db:"status"`
	CreatedAt   time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at" db:"updated_at"`

	// 关联字段（不存储在数据库中）
	Children []*Category `json:"children,omitempty" db:"-"`
	Parent   *Category   `json:"parent,omitempty" db:"-"`
}

// CategoryTree 分类树结构
type CategoryTree struct {
	Categories []*Category `json:"categories"`
}

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string  `json:"name" binding:"required,max=100"`
	Description *string `json:"description" binding:"omitempty,max=500"`
	ParentID    *string `json:"parent_id" binding:"omitempty,uuid"`
	IconURL     *string `json:"icon_url" binding:"omitempty,url"`
	SortOrder   *int    `json:"sort_order" binding:"omitempty,min=0"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        *string         `json:"name" binding:"omitempty,max=100"`
	Description *string         `json:"description" binding:"omitempty,max=500"`
	ParentID    *string         `json:"parent_id" binding:"omitempty,uuid"`
	IconURL     *string         `json:"icon_url" binding:"omitempty,url"`
	SortOrder   *int            `json:"sort_order" binding:"omitempty,min=0"`
	Status      *CategoryStatus `json:"status" binding:"omitempty,oneof=ACTIVE INACTIVE"`
}

// CategoryListRequest 分类列表请求
type CategoryListRequest struct {
	Level    *int            `form:"level" binding:"omitempty,min=1"`
	Status   *CategoryStatus `form:"status" binding:"omitempty,oneof=ACTIVE INACTIVE"`
	ParentID *string         `form:"parent_id" binding:"omitempty,uuid"`
	Page     int             `form:"page" binding:"omitempty,min=1"`
	PageSize int             `form:"page_size" binding:"omitempty,min=1,max=100"`
}

// CategoryResponse 分类响应
type CategoryResponse struct {
	*Category
	ChildrenCount int `json:"children_count"`
	ProductCount  int `json:"product_count"`
}

// Validate 验证分类数据
func (c *Category) Validate() error {
	if c.Name == "" {
		return fmt.Errorf("category name is required")
	}

	if len(c.Name) > 100 {
		return fmt.Errorf("category name too long")
	}

	if c.Level < 1 {
		return fmt.Errorf("category level must be greater than 0")
	}

	return nil
}

// IsRoot 判断是否为根分类
func (c *Category) IsRoot() bool {
	return c.ParentID == nil
}

// GetFullPath 获取完整路径
func (c *Category) GetFullPath() string {
	if c.Path != nil {
		return *c.Path
	}
	return "/" + c.Name
}

// Value 实现 driver.Valuer 接口
func (cs CategoryStatus) Value() (driver.Value, error) {
	return string(cs), nil
}

// Scan 实现 sql.Scanner 接口
func (cs *CategoryStatus) Scan(value interface{}) error {
	if value == nil {
		*cs = CategoryStatusActive
		return nil
	}

	switch s := value.(type) {
	case string:
		*cs = CategoryStatus(s)
	case []byte:
		*cs = CategoryStatus(s)
	default:
		return fmt.Errorf("cannot scan %T into CategoryStatus", value)
	}

	return nil
}

// MarshalJSON 自定义JSON序列化
func (cs CategoryStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(cs))
}

// UnmarshalJSON 自定义JSON反序列化
func (cs *CategoryStatus) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*cs = CategoryStatus(s)
	return nil
}

// IsActive 检查分类是否激活
func (c *Category) IsActive() bool {
	return c.Status == CategoryStatusActive
}

// CategoryCreateRequest 创建分类请求
type CategoryCreateRequest struct {
	Name        string  `json:"name" binding:"required,max=100"`
	Description *string `json:"description" binding:"omitempty,max=500"`
	ParentID    *string `json:"parent_id" binding:"omitempty,uuid"`
	IconURL     *string `json:"icon_url" binding:"omitempty,url"`
	SortOrder   *int    `json:"sort_order" binding:"omitempty,min=0"`
}

// CategoryUpdateRequest 更新分类请求
type CategoryUpdateRequest struct {
	Name        *string `json:"name" binding:"omitempty,max=100"`
	Description *string `json:"description" binding:"omitempty,max=500"`
	ParentID    *string `json:"parent_id" binding:"omitempty,uuid"`
	IconURL     *string `json:"icon_url" binding:"omitempty,url"`
	SortOrder   *int    `json:"sort_order" binding:"omitempty,min=0"`
}

// CategoryListResponse 分类列表响应
type CategoryListResponse struct {
	Categories []*Category `json:"categories"`
	Total      int         `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
}

// CategoryTreeResponse 分类树响应
type CategoryTreeResponse struct {
	Tree []*Category `json:"tree"`
}

// CategoryPathResponse 分类路径响应
type CategoryPathResponse struct {
	Path []*Category `json:"path"`
}

// CategoryStatusUpdateRequest 分类状态更新请求
type CategoryStatusUpdateRequest struct {
	Status CategoryStatus `json:"status" binding:"required"`
}

// Validate 验证创建分类请求
func (req *CategoryCreateRequest) Validate() error {
	if req.Name == "" {
		return fmt.Errorf("category name is required")
	}
	if len(req.Name) > 100 {
		return fmt.Errorf("category name too long")
	}
	return nil
}

// Validate 验证更新分类请求
func (req *CategoryUpdateRequest) Validate() error {
	if req.Name != nil && *req.Name == "" {
		return fmt.Errorf("category name cannot be empty")
	}
	if req.Name != nil && len(*req.Name) > 100 {
		return fmt.Errorf("category name too long")
	}
	return nil
}

// Validate 验证分类列表请求
func (req *CategoryListRequest) Validate() error {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	return nil
}
