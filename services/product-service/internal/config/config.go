package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	Kafka    KafkaConfig    `mapstructure:"kafka"`
	Cache    CacheConfig    `mapstructure:"cache"`
	Search   SearchConfig   `mapstructure:"search"`
	Logging  LoggingConfig  `mapstructure:"logging"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host string `mapstructure:"host"`
	Port string `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	Charset         string `mapstructure:"charset"`
	ParseTime       bool   `mapstructure:"parse_time"`
	Loc             string `mapstructure:"loc"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Password     string `mapstructure:"password"`
	Database     int    `mapstructure:"database"`
	PoolSize     int    `mapstructure:"pool_size"`
	MinIdleConns int    `mapstructure:"min_idle_conns"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers  []string      `mapstructure:"brokers"`
	Topics   TopicsConfig  `mapstructure:"topics"`
	Producer ProducerConfig `mapstructure:"producer"`
	Consumer ConsumerConfig `mapstructure:"consumer"`
}

// TopicsConfig Kafka主题配置
type TopicsConfig struct {
	ProductEvents string `mapstructure:"product_events"`
}

// ProducerConfig Kafka生产者配置
type ProducerConfig struct {
	RetryMax     int `mapstructure:"retry_max"`
	BatchSize    int `mapstructure:"batch_size"`
	BatchTimeout int `mapstructure:"batch_timeout"`
}

// ConsumerConfig Kafka消费者配置
type ConsumerConfig struct {
	GroupID         string `mapstructure:"group_id"`
	AutoOffsetReset string `mapstructure:"auto_offset_reset"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	ProductDetailTTL int `mapstructure:"product_detail_ttl"`
	CategoryTreeTTL  int `mapstructure:"category_tree_ttl"`
	SearchResultTTL  int `mapstructure:"search_result_ttl"`
	ProductListTTL   int `mapstructure:"product_list_ttl"`
}

// SearchConfig 搜索配置
type SearchConfig struct {
	PageSizeDefault int `mapstructure:"page_size_default"`
	PageSizeMax     int `mapstructure:"page_size_max"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// GetDSN 获取数据库连接字符串
func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		d.Username, d.Password, d.Host, d.Port, d.Database, d.Charset, d.ParseTime, d.Loc)
}

// GetRedisAddr 获取Redis地址
func (r *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", r.Host, r.Port)
}

// GetProductDetailTTL 获取商品详情缓存TTL
func (c *CacheConfig) GetProductDetailTTL() time.Duration {
	return time.Duration(c.ProductDetailTTL) * time.Second
}

// GetCategoryTreeTTL 获取分类树缓存TTL
func (c *CacheConfig) GetCategoryTreeTTL() time.Duration {
	return time.Duration(c.CategoryTreeTTL) * time.Second
}

// GetSearchResultTTL 获取搜索结果缓存TTL
func (c *CacheConfig) GetSearchResultTTL() time.Duration {
	return time.Duration(c.SearchResultTTL) * time.Second
}

// GetProductListTTL 获取商品列表缓存TTL
func (c *CacheConfig) GetProductListTTL() time.Duration {
	return time.Duration(c.ProductListTTL) * time.Second
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置环境变量前缀
	viper.SetEnvPrefix("PRODUCT_SERVICE")
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}
